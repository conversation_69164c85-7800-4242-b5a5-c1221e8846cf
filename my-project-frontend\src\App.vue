<script setup>
import { useDark, useToggle } from '@vueuse/core'
import {onMounted, provide, ref} from "vue";
import {unauthorized} from "@/net";
import {apiUserInfo} from "@/net/api/user";

useDark({
  selector: 'html',
  attribute: 'class',
  valueDark: 'dark',
  valueLight: 'light'
})

const loading = ref()
provide('userLoading', loading)

useDark({
  onChanged(dark) { useToggle(dark) }
})

onMounted(() => {
    if(!unauthorized()) {
        apiUserInfo(loading)
    }
})
</script>

<template>
  <header>
    <div class="wrapper">
        <router-view/>
    </div>
  </header>
</template>

<style scoped>
header {
  line-height: 1.5;
}
</style>
