# 开发环境配置
springdoc:
  paths-to-match: /api/**
  swagger-ui:
    operations-sorter: alpha
spring:
  mail:
    host: smtp.qq.com
    username: <EMAIL>
    password: ddcdamwejevqfebh
    port: 465
    properties:
      mail:
        smtp:
          ssl:
            enable: true
  rabbitmq:
    addresses: localhost
    username: guest
    password: guest
    virtual-host: /
  datasource:
    url: *********************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  security:
    jwt:
      key: 'abcdefghijklmn'
      expire: 72
      limit:
        base: 10
        upgrade: 300
        frequency: 30
    filter:
      order: -100
  web:
    verify:
      mail-limit: 60
    flow:
      period: 3
      limit: 50
      block: 30
    cors:
      origin: '*'
      credentials: false
      methods: '*'
  minio:
    endpoint: 'http://localhost:9000'
    username: 'minio'
    password: 'password'
  weather:
    key: 115bcaf74fb24f73844365290d85862b
