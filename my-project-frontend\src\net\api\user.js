import {get, post} from "@/net";
import {useStore} from "@/store";

export const apiUserInfo = (loadingRef) => {
    if(loadingRef) loadingRef.value = true
    get('/api/user/info', (data) => {
        const store = useStore();
        store.user = data
        if(loadingRef) loadingRef.value = false
    })
}

export const apiUserChangePassword = (form, success) =>
    post('/api/user/change-password', form, success)

export const apiUserPrivacy = (success) =>
    get('/api/user/privacy', success)

export const apiUserPrivacySave = (data, loadingRef, success) => {
    loadingRef.value = true
    post('/api/user/save-privacy', data, () => {
        loadingRef.value = false
        success()
    })
}

export const apiUserDetailSave = (form, success, failure) =>
    post('/api/user/save-details', form, success, failure)

export const apiUserDetail = (success) =>
    get('/api/user/details', success)
